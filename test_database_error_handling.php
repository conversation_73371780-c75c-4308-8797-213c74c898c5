<?php
/**
 * Test script for database error handling improvements
 * This script tests the enhanced error handling and logging functionality
 */

// Set up minimal environment
define('DEBUG_MODE', true);
define('API_RUN', false);

// Include necessary files
$path = ['fs_app_root' => __DIR__ . '/'];
require_once 'system/paths.php';
require_once 'system/functions/functions.php';

// Load the path schema
$schema = require_once 'system/config/path_schema.php';
$path = build_paths($path, $schema);
build_constants($path);

require_once 'system/functions/database.php';
require_once 'system/autoloader.php';

use system\database;

echo "<h1>Database Error Handling Test</h1>\n";
echo "<p>Testing improved database error handling and logging...</p>\n";

// Test 1: Test invalid table name
echo "<h2>Test 1: Invalid Table Name</h2>\n";
try {
    $result = database::table('nonexistent_table')->get();
    echo "<p style='color: green;'>Unexpected: Query succeeded</p>\n";
} catch (Exception $e) {
    echo "<p style='color: orange;'>Expected: Caught exception - " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 2: Test invalid column in insert
echo "<h2>Test 2: Invalid Column in Insert</h2>\n";
try {
    $result = database::table('autobooks_navigation')->insert([
        'invalid_column' => 'test_value',
        'another_invalid' => 'test_value2'
    ]);
    echo "<p style='color: green;'>Unexpected: Insert succeeded</p>\n";
} catch (Exception $e) {
    echo "<p style='color: orange;'>Expected: Caught exception - " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 3: Test SQL syntax error with parameters
echo "<h2>Test 3: SQL Syntax Error</h2>\n";
try {
    // This should trigger the exact error you were seeing
    $stmt = tep_db_query("SELECT * FROM autobooks_navigation WHERE invalid_syntax ?", null, ['test_param']);
    echo "<p style='color: green;'>Unexpected: Query succeeded</p>\n";
} catch (Exception $e) {
    echo "<p style='color: orange;'>Expected: Caught exception - " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 4: Check if log files were created
echo "<h2>Test 4: Log File Creation</h2>\n";
$log_files = [
    FS_LOGS . 'database_errors_logfile.log',
    FS_LOGS . 'legacy_database_errors_logfile.log',
    FS_LOGS . 'navigation_errors_logfile.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $size = filesize($log_file);
        echo "<p style='color: green;'>✓ Log file exists: " . basename($log_file) . " (Size: {$size} bytes)</p>\n";
        
        // Show last few lines of the log
        $lines = file($log_file);
        $last_lines = array_slice($lines, -3);
        echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>";
        echo "Last 3 lines:\n";
        echo htmlspecialchars(implode('', $last_lines));
        echo "</pre>\n";
    } else {
        echo "<p style='color: red;'>✗ Log file not found: " . basename($log_file) . "</p>\n";
    }
}

echo "<h2>Test Complete</h2>\n";
echo "<p>Check the log files in the " . FS_LOGS . " directory for detailed error information.</p>\n";
?>
