<?php

use system\database;

/**
 * Build application paths based on schema
 * 
 * @param array $path Initial path data
 * @return array Complete path data
 */
function build_paths(array $path = [], $schema = []): array {
    // Initialize base paths

    if (isset($schema['system_views'])){
        $path['system_views'] = $schema['system_views'];
    }

    if (!isset($path['fs_app_root'])) {
        $path['fs_app_root'] = str_replace("system", "", __DIR__);
    }

    // Ensure the path ends with a slash for proper concatenation
    if (!str_ends_with($path['fs_app_root'], '/')) {
        $path['fs_app_root'] .= '/';
    }

    // Load path utilities first (needed for fs_path and normalize_path functions)
    require_once $path['fs_app_root'] . 'system/functions/path_utils.php';

    // Now we can safely normalize the path, preserving leading slash for absolute paths
    $is_absolute = str_starts_with($path['fs_app_root'], '/');
    $normalized = normalize_path($path['fs_app_root']);
    $path['fs_app_root'] = ($is_absolute ? '/' : '') . $normalized . '/';

    // Load request information
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    $path['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
    $path['domain'] = $_SERVER['SERVER_NAME'] ?? '';
    $path['script_name'] = $_SERVER['SCRIPT_NAME'];

    // Clean request URI
    $has_params = strpos($path['request_uri'], '?');
    $path['request_uri'] = $has_params ? substr($_SERVER['REQUEST_URI'], 0, $has_params) : $_SERVER['REQUEST_URI'] ?? '';

    // Set document root paths
    if (!isset($path['fs_doc_root'])) $path['fs_doc_root'] = fs_path($_SERVER['DOCUMENT_ROOT']);
    if (!isset($path['doc_root'])) {
        $doc_root = fs_path($_SERVER['DOCUMENT_ROOT']);
        // Ensure doc_root has trailing slash for proper path concatenation
        $path['doc_root'] = rtrim($doc_root, '/') . '/';
    }

    $path['fs_app'] = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/';


    
    // Process resource paths
    if (isset($schema['resources']) && is_array($schema['resources'])) {
        foreach ($schema['resources'] as $dir => $location) {
            $path['fs_' . $dir] = join_paths($path['fs_app_root'], $location) . '/';
        }
    }

    // Process system paths
    if (isset($schema['system']) && is_array($schema['system'])) {
        foreach ($schema['system'] as $dir => $location) {
            $key = $dir === 'root' ? 'fs_system' : 'fs_sys_' . $dir;
            $path[$key] = join_paths($path['fs_app_root'], $location);

            // Add trailing slash for directories
            if ($dir !== 'db_class') {
                $path[$key] .= '/';
            }
        }
    }

    // Set external paths
    if (isset($schema['external']) && is_array($schema['external'])) {
        foreach ($schema['external'] as $key => $location) {
            $path['fs_' . $key] = $location;
        }
    }
    
    // Set app paths
    // Normalize both paths to use forward slashes for consistent comparison
    $normalized_doc_root = str_replace('\\', '/', $path['doc_root']);
    $normalized_fs_app_root = str_replace('\\', '/', $path['fs_app_root']);

    $path['app_root'] = web_path(str_replace($normalized_doc_root, '', $normalized_fs_app_root));
    $path['app_path'] = normalize_path(str_replace($path['app_root'], '', $path['request_uri']));
    $path['path_parts'] = explode('/', $path['app_path']);
    $path['top_level'] = $path['path_parts'][0] ?? '';

    // Only set fs_app_path if the required view paths exist
    if ($path['top_level'] === 'system' && isset($path['fs_sys_views'])) {
        $path['fs_app_path'] = fs_path(join_paths($path['fs_sys_views'], $path['app_path']));
    } elseif ($path['top_level'] !== 'system' && isset($path['fs_views'])) {
        $path['fs_app_path'] = fs_path(join_paths($path['fs_views'], $path['app_path']));
    } else {
        // Fallback if view paths are not available
        $path['fs_app_path'] = fs_path(join_paths($path['fs_app_root'], 'resources/views', $path['app_path']));
    }

    $path['app_path'] = str_replace('get_view/', '/', $path['app_path']);
    
    // Set current page
    if (count($path['path_parts']) > 1) {
        $last_element = count($path['path_parts']) - 1;
        $path['current_page'] = $path['path_parts'][$last_element];
    } else {
        $path['current_page'] = $path['top_level'];
    }
    $path['current_page'] = explode('?', $path['current_page'])[0];
    
    // Handle HTMX source paths
    $path['set_by'] = 'default';
    $path['source_path'] = '';
    $path['source_page'] = '';
    $path['source_path_parts'] = '';
    $path['source_app_path'] = '';
    
    if (isset($_SERVER['HTTP_HX_CURRENT_URL'])) {
        $path['set_by'] = 'HTTP_HX_CURRENT_URL';
        $path['hx_current_url'] = $_SERVER['HTTP_HX_CURRENT_URL'];
        $path['hx_current_url_parts'] = parse_url($_SERVER['HTTP_HX_CURRENT_URL']);
        $path['source_path_parts'] = explode('/', normalize_path($path['hx_current_url_parts']['path']));
        $path['source_page'] = array_pop($path['source_path_parts']);
        $path['source_path'] = web_path(implode('/', $path['source_path_parts']));
        $path['source_app_path'] = normalize_path(str_replace($path['app_root'], '', $path['source_path']));
        $path['source_app_path_parts'] = explode('/', $path['source_app_path']);
        $path['source_fs_path'] = fs_path(join_paths($path['fs_app_root'], 'resources/views', $path['source_app_path']));
    }
    
    // Set database class path and load it
    if (isset($path['fs_system'])) {
        $path['fs_sys_db_class'] = fs_path(join_paths($path['fs_system'], 'classes/database.class.php'));
        require_once $path['fs_sys_db_class'];
    } else {
        // Fallback if fs_system is not available
        $path['fs_sys_db_class'] = fs_path(join_paths($path['fs_app_root'], 'system/classes/database.class.php'));
        require_once $path['fs_sys_db_class'];
    }
    
    // Set action parameter if not set
    global $input_params;
    if (!isset($input_params['action'])) {
        $temp = explode('/', $path['request_uri']);
        $temp2 = array_pop($temp);
        $input_params['action'] = $temp2;
    }
    
    return $path;
}

/**
 * Define constants from path array
 *
 * @param array $path Path data
 * @param bool $generate_ide_helper Whether to generate/update IDE helper file
 * @return void
 */
function build_constants($path, $generate_ide_helper = false): void {
    $constants_defined = [];

    foreach ($path as $key => $value) {
        $constant_name = strtoupper($key);
        if (defined($constant_name)) continue;

        if (is_string($value)) {
            // Preserve absolute paths and trailing slashes when normalizing
            $is_absolute = str_starts_with($value, '/');
            $has_trailing_slash = str_ends_with($value, '/');
            $normalized = normalize_path($value);

            // Reconstruct the path preserving leading and trailing slashes
            $value = ($is_absolute ? '/' : '') . $normalized . ($has_trailing_slash ? '/' : '');
        }

        define($constant_name, $value);

        // Track constants for IDE helper generation
        if ($generate_ide_helper) {
            $constants_defined[$constant_name] = [
                'value' => $value,
                'type' => gettype($value),
                'source_key' => $key
            ];
        }
    }

    // Generate IDE helper file if requested
    if ($generate_ide_helper && !empty($constants_defined)) {
        generate_ide_helper_constants($constants_defined);
    }
}

/**
 * Generate IDE helper file for constants
 *
 * @param array $constants_defined Array of constants with metadata
 * @return void
 */
function generate_ide_helper_constants($constants_defined): void {
    $app_root = $constants_defined['FS_APP_ROOT']['value'] ?? dirname(__DIR__) . '/';
    $helper_file = $app_root . '_ide_helper_constants.php';

    // Load existing constants from schema to get descriptions
    $schema_file = $app_root . 'system/config/path_schema.php';
    $schema = file_exists($schema_file) ? include($schema_file) : [];

    $content = "<?php\n";
    $content .= "/**\n";
    $content .= " * IDE Helper for Dynamically Generated Constants\n";
    $content .= " * \n";
    $content .= " * This file is auto-generated to help IDEs recognize dynamically created constants.\n";
    $content .= " * Generated on: " . date('Y-m-d H:i:s') . "\n";
    $content .= " * \n";
    $content .= " * DO NOT EDIT THIS FILE MANUALLY - it will be overwritten.\n";
    $content .= " * To regenerate: php system/generate_ide_helper.php\n";
    $content .= " */\n\n";

    $content .= "// Prevent execution if included directly\n";
    $content .= "if (!defined('FS_APP_ROOT')) {\n";
    $content .= "    die('This file should not be executed directly.');\n";
    $content .= "}\n\n";

    // Group constants by category for better organization
    $categories = [
        'FS_' => 'File System Paths',
        'APP_' => 'Application Paths',
        'DOC_' => 'Document Root Paths',
        'REQUEST_' => 'Request Information',
        'DOMAIN' => 'Domain Information',
        'ROUTE_' => 'Route Information',
        'USER_' => 'User Information'
    ];

    $categorized_constants = [];
    $other_constants = [];

    foreach ($constants_defined as $name => $info) {
        $categorized = false;
        foreach ($categories as $prefix => $category) {
            if (str_starts_with($name, $prefix)) {
                $categorized_constants[$category][$name] = $info;
                $categorized = true;
                break;
            }
        }
        if (!$categorized) {
            $other_constants[$name] = $info;
        }
    }

    // Generate constants by category
    foreach ($categorized_constants as $category => $constants) {
        $content .= "// " . str_repeat('=', 60) . "\n";
        $content .= "// $category\n";
        $content .= "// " . str_repeat('=', 60) . "\n\n";

        foreach ($constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema);
        }
        $content .= "\n";
    }

    // Generate other constants
    if (!empty($other_constants)) {
        $content .= "// " . str_repeat('=', 60) . "\n";
        $content .= "// Other Constants\n";
        $content .= "// " . str_repeat('=', 60) . "\n\n";

        foreach ($other_constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema);
        }
    }

    // Write the file
    file_put_contents($helper_file, $content);
}

/**
 * Generate a single constant definition with documentation
 *
 * @param string $name Constant name
 * @param array $info Constant information
 * @param array $schema Path schema for descriptions
 * @return string
 */
function generate_constant_definition($name, $info, $schema): string {
    $value = $info['value'];
    $type = $info['type'];
    $source_key = $info['source_key'];

    // Generate description based on constant name and schema
    $description = generate_constant_description($name, $source_key, $schema);

    $content = "/**\n";
    $content .= " * $description\n";
    $content .= " * \n";
    $content .= " * @var $type\n";
    $content .= " * @source $source_key\n";
    $content .= " */\n";

    // Format the value for the define statement
    if (is_string($value)) {
        $formatted_value = "'" . addslashes($value) . "'";
    } elseif (is_bool($value)) {
        $formatted_value = $value ? 'true' : 'false';
    } elseif (is_null($value)) {
        $formatted_value = 'null';
    } else {
        $formatted_value = var_export($value, true);
    }

    $content .= "if (!defined('$name')) define('$name', $formatted_value);\n\n";

    return $content;
}

/**
 * Generate description for a constant based on its name and source
 *
 * @param string $name Constant name
 * @param string $source_key Source key from path array
 * @param array $schema Path schema
 * @return string
 */
function generate_constant_description($name, $source_key, $schema): string {
    // Common descriptions for known patterns
    $descriptions = [
        'FS_APP_ROOT' => 'File system path to application root directory',
        'FS_SYSTEM' => 'File system path to system directory',
        'FS_CLASSES' => 'File system path to classes directory',
        'FS_FUNCTIONS' => 'File system path to functions directory',
        'FS_VIEWS' => 'File system path to views directory',
        'FS_CONFIG' => 'File system path to config directory',
        'FS_TEMPLATES' => 'File system path to templates directory',
        'FS_COMPONENTS' => 'File system path to components directory',
        'FS_UPLOADS' => 'File system path to uploads directory',
        'FS_API' => 'File system path to API directory',
        'FS_LOGS' => 'File system path to logs directory',
        'FS_CACHE' => 'File system path to cache directory',
        'FS_TEMP' => 'File system path to temporary files directory',
        'APP_ROOT' => 'Web path to application root',
        'APP_PATH' => 'Current application path relative to root',
        'REQUEST_URI' => 'Current request URI',
        'DOMAIN' => 'Current domain name',
        'DOC_ROOT' => 'Document root path',
        'ROUTE_TREE' => 'Application route tree structure',
        'ROUTE_LIST' => 'Flat list of application routes',
        'ROUTES' => 'Database routes data',
        'USER_ROLE' => 'Current user role'
    ];

    if (isset($descriptions[$name])) {
        return $descriptions[$name];
    }

    // Generate description based on patterns
    if (str_starts_with($name, 'FS_SYS_')) {
        $part = strtolower(substr($name, 7));
        return "File system path to system $part directory";
    }

    if (str_starts_with($name, 'FS_')) {
        $part = strtolower(substr($name, 3));
        return "File system path to $part directory";
    }

    if (str_starts_with($name, 'APP_')) {
        $part = strtolower(substr($name, 4));
        return "Application $part path";
    }

    // Default description
    return "Dynamically generated constant from $source_key";
}

/**
 * Build routes from database
 * 
 * @param array $path Path data
 * @return array Route data
 */
function build_routes($path): array {
    $route_tree = [];
    $route_list = [];
    if (!defined('API_RUN') || !API_RUN) {
        $routes = database::table('autobooks_navigation as nav')
            ->select(['id', 'parent_path', 'route_key', 'name', 'icon', 'required_roles', 'show_navbar'])
            ->cast([
                'required_roles' => 'array',
                'show_navbar' => 'bool'
            ])
            ->get();
        foreach ($routes as $key => $route) {
            if (in_array($route, ['icon', 'name', 'sub_folder'])) continue;
            $route_list[$route['route_key']] = $route;
            // Process parent path to build tree
            if (empty($route['parent_path'])) {
                // Top-level route
                $route_tree[$route['route_key']] = [
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'],
                    'show_navbar' => $route['show_navbar']
                ];
            } else {
                // Nested route - find the correct parent
                $parent_path = normalize_path($route['parent_path']);
                $path_parts = array_filter(explode('/', $parent_path));

                // Navigate to the correct position in the tree
                $current = &$route_tree;
                foreach ($path_parts as $part) {
                    if (!isset($current[$part]['sub_folder'])) {
                        $current[$part]['sub_folder'] = [];
                    }
                    $current = &$current[$part]['sub_folder'];
                }

                // Add the route to its parent's sub_folder
                $current[$route['route_key']] = [
                    'name' => $route['name'],
                    'icon' => $route['icon'],
                    'required_roles' => $route['required_roles'],
                    'show_navbar' => $route['show_navbar']
                ];
            }
        };

        define('ROUTE_TREE', $route_tree);
        define('ROUTE_LIST', $route_list);
        define('ROUTES', $routes);
    }
    return $routes ?? [];
}

/**
 * Remove keywords from path
 * 
 * @param string $path Path to process
 * @return string Processed path
 */
function tcs_remove_keywords($path): string {
    return str_replace('get_view/', '/', $path);
}

/**
 * Legacy path normalization function
 * 
 * @param string $path Path to normalize
 * @return string Normalized path
 */
function tcs_path($path): string {
    return normalize_path($path);
}

/**
 * Build API path
 * 
 * @param string $path API endpoint
 * @return string Full API path
 */
function tcs_api($path): string {
    return '/' . APP_ROOT . 'api/' . $path;
}
?>