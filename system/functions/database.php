<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2013 osCommerce

  Released under the GNU General Public License
*/

// Global variable to hold the PDO instance
$pdo = null;


function tep_db_connect($server, $username, $password, $database) {
    // function body...
    global $pdo;
    //print_rr($pdo,'$pdo',false);
    try {
        $dsn = "mysql:host=$server;dbname=$database;charset=utf8";
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
        ];
        
       // print_rr(['dsn' => $dsn, 'username' => $username, 'password' => $password, 'options' => $options],'db', false);

        $pdo = new PDO($dsn, $username, $password, $options);
        $pdo->exec("set session sql_mode=''");
        return $pdo;
    } catch (PDOException $e) {
        print_rr($e->getMessage(),'tep_db_connect',false);
        die('Connection failed: ' . $e->getMessage());
    }
}

function tep_db_close() {
    global $pdo;
    // In PDO, to close the connection, you set the PDO object to null.
    $pdo = null;
    return true;
}

function tep_db_error($query, $errorInfo, $params = []) {
    // Enhanced logging with more context
    $errorData = [
        'sql_state' => $errorInfo[0] ?? 'Unknown',
        'driver_error_code' => $errorInfo[1] ?? 'Unknown',
        'driver_error_message' => $errorInfo[2] ?? 'Unknown',
        'query' => $query,
        'parameters' => $params,
        'user_id' => $_SESSION['user_id'] ?? 'Anonymous',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];

    // Log to custom database error log if tcs_log is available
    if (function_exists('tcs_log')) {
        tcs_log($errorData, 'legacy_database_errors');
    }

    // Legacy logging for backwards compatibility
    if (defined('STORE_DB_TRANSACTIONS') && (STORE_DB_TRANSACTIONS == 'true')) {
        error_log('ERROR: [' . $errorInfo[1] . '] ' . $errorInfo[2] . "\nQuery: " . $query . "\nParams: " . json_encode($params) . "\n", 3, STORE_PAGE_PARSE_TIME_LOG);
    }

    // Enhanced error display based on context
    if (defined('API_RUN') && API_RUN) {
        // For API requests, return JSON error
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Database error occurred',
            'details' => defined('DEBUG_MODE') && DEBUG_MODE ? $errorInfo[2] : 'Please contact support',
            'error_id' => uniqid(),
            'sql_state' => $errorInfo[0] ?? 'Unknown'
        ]);
        exit;
    } else {
        // For web requests, show formatted error
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px; font-family: monospace;">
                <h3>Legacy Database Error (Debug Mode)</h3>
                <p><strong>SQL State:</strong> ' . htmlspecialchars($errorInfo[0] ?? 'Unknown') . '</p>
                <p><strong>Error Code:</strong> ' . htmlspecialchars($errorInfo[1] ?? 'Unknown') . '</p>
                <p><strong>Error Message:</strong> ' . htmlspecialchars($errorInfo[2] ?? 'Unknown') . '</p>
                <p><strong>Query:</strong> ' . htmlspecialchars($query) . '</p>
                <p><strong>Parameters:</strong> ' . htmlspecialchars(json_encode($params)) . '</p>
                </div>');
        } else {
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;">
                <h3>Database Error</h3>
                <p>A database error occurred. Please try again later or contact support if the problem persists.</p>
                <p>Error ID: ' . uniqid() . '</p>
                </div>');
        }
    }
}

function tep_db_query($query, $pdo = null,$params = []) {
    if ($pdo === null) {
        global $pdo;
       //print_rr($pdo,'$pdo');
    }

    // Enhanced logging with parameters
    if (defined('STORE_DB_TRANSACTIONS') && STORE_DB_TRANSACTIONS === 'true') {
        error_log('QUERY: ' . $query . "\nPARAMS: " . json_encode($params) . "\n", 3, STORE_PAGE_PARSE_TIME_LOG);
    }

    try {
        // Check if parameters are provided for a prepared statement
        if (!empty($params)) {
            $stmt = $pdo->prepare($query);
            $stmt->execute($params);
        } else {
            $stmt = $pdo->query($query);
        }
        return $stmt;
    } catch (PDOException $e) {
        // Pass parameters to error handler for better debugging
        tep_db_error($query, $e->errorInfo, $params);
    }
}

function tep_db_perform($table, $data, $action = 'insert', $parameters = '', $pdo = null) {
    if ($pdo === null) global $pdo;
    
    if ($action == 'insert') {
        $columns = implode(', ', array_keys($data));
        $placeholders = '';
        $values = [];

        foreach ($data as $key => $value) {// Check if the value looks like an SQL function
            if (is_string($value) && strpos($value, 'now()') !== false) { // Check if the value looks like an SQL function
                $placeholders .= "$value, ";
            } else {
                $placeholders .= '?, ';
                $values[] = $value;
            }            
        }
        $placeholders = rtrim($placeholders, ', ');
        $query = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute($values);
            return $stmt;
        } catch (PDOException $e) {
            // Pass values to error handler for better debugging
            tep_db_error($query, $e->errorInfo, $values);
        }
    } elseif ($action == 'update') {
        $set = '';
        $values = [];

        foreach ($data as $column => $value) {
            if (is_string($value) && strpos($value, '()') !== false) { // Check if the value looks like an SQL function
                $set .= "$column = $value, ";
            } else {
                $set .= "$column = ?, ";
                $values[] = $value;
            }
        }
        $set = rtrim($set, ', ');
        $query = "UPDATE $table SET $set WHERE $parameters";
        try {
            $stmt = $pdo->prepare($query);
            $stmt->execute($values);
            return $stmt;
        } catch (PDOException $e) {
            tep_db_error($query, $e->errorInfo);
        }
    }
}


function tep_db_fetch_array($stmt) {
    try {
        return $stmt->fetch();
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}

function tep_db_fetch_all($stmt) {
    try {
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}
function tep_db_fetch_col($stmt) {
    try {
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}

function tep_db_num_rows($stmt) {
    try {
        return $stmt->rowCount();
    } catch (PDOException $e) {
        tep_db_error('Num Rows', $e->errorInfo);
    }
}

function tep_db_data_seek($stmt, $row_number) {
  tep_db_data_seek_pdo($stmt, $row_number);
}

function tep_db_data_seek_pdo($pdo_statement, $row_number) {
  // Fetch all results into an array
  $results = $pdo_statement->fetchAll(PDO::FETCH_ASSOC);
  
  // Check if the desired row exists
  if (isset($results[$row_number])) {
      // Return the desired row
      return $results[$row_number];
  } else {
      // Return false if the row does not exist
      return false;
  }
}

function tep_db_insert_id($pdo = null) {
    if ($pdo === null) {
        global $pdo;
    }
    try {
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        tep_db_error('Insert ID', $e->errorInfo);
    }
}

function tep_db_free_result($stmt) {
    try {
        $stmt = null;
        return true;
    } catch (PDOException $e) {
        tep_db_error('Free Result', $e->errorInfo);
    }
}

function tep_db_fetch_object($stmt) {
    try {
        return $stmt->fetchObject();
    } catch (PDOException $e) {
        tep_db_error('Fetch Object', $e->errorInfo);
    }
}

function tep_db_input($string, $pdo = null) {
    if ($pdo === null) {
        global $pdo;
    }
    //print_rr($string);
    try {
        return substr($pdo->quote($string), 1, -1); // remove the wrapping quotes as tep_db_input() didn't add them
    } catch (PDOException $e) {
        tep_db_error('DB Input', $e->errorInfo);
    }
}

function tep_db_output($string) {
  return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
function tep_db_prepare_input($string) {
  if (is_string($string)) {
      return trim($string);
  } elseif (is_array($string)) {
      foreach ($string as $key => $value) {
          $string[$key] = tep_db_prepare_input($value);
      }
  }
  return $string;
}

function tep_db_affected_rows($stmt) {
    return $stmt->rowCount();
  }

  
  function tep_db_get_server_info($pdo = null) {
    if ($pdo === null) global $pdo;

    // Assuming $$link is a PDO instance
    $stmt = $pdo->query("SELECT VERSION()");
    $version = $stmt->fetchColumn();

    return $version;
}

function tcs_db_build_criteria(array $criteria, array $schema): array {
    $where = $limit = $begin = $order_by = $end = "";
    $count = 0;
    $query_columns = $wheres = $multi_wheres = $searches = [];
    $query_tables = array_fill_keys(array_keys($schema), null);

    $populate_column = function($column = "") use (&$query_columns,&$query_tables,$schema): bool{
        if ($column == "") return false;
        $db = parse_table_column_name($column,$schema);
        if (!$db) return false;
        $query_columns[ $db['table'].$db['column'] ] = build_sql_select_entry($db['column'],$db['table']);
        print_rr('looking for '.$db['table'] . ' in query_tables ' );
        if ( key_exists($db['table'],$query_tables) ) {
            $query_tables[$db['table']] = $schema[$db['table']]['query'];
            print_rr('  found ');
        }
        print_rr($query_tables,'$populate_column qt');
        return true;
    };
    if ($criteria) {
        if (isset($criteria["search"])) {
            foreach ($criteria["search_columns"] as $column) {
                $populate_column($column);
                $searches[] = "{$column} LIKE '%{$criteria["search"]}%'";
            }
            $wheres[] = '(' . implode(' OR ', $searches) . ')';
        }
        if (isset($criteria["column"])) {
            if (isset($criteria["column"][0]) && isset($criteria["column"][1])) {
                $column = $criteria["column"][0];
                $operator = match ($criteria["column"][1]) {
                    "not_equals" => "<>",
                    "less_than" => "<",
                    "greater_than" => ">",
                    "less_than_or_equals" => "<=",
                    "greater_than_or_equals" => ">=",
                    "like" => "LIKE",
                    "not_like" => "NOT LIKE",
                    "in" => "IN",
                    "not_in" => "NOT IN",
                    "between" => "BETWEEN",
                    "not_between" => "NOT BETWEEN",
                    "null" => "IS NULL",
                    "not_null" => "IS NOT NULL",
                    default => "="
                };
                $data = "";
                if (isset($criteria["column"][2])) {
                    $data = $criteria["column"][2];
                }
                $populate_column($column);
                $column = implode('.',parse_table_column_name($column,$schema));
                $wheres[] = "$column $operator $data";
                print_rr($where,'where where?');
            }
        }
        if (isset($criteria["where"])) {
            if (is_string($criteria["where"])) {
                $where = "WHERE " . $criteria["where"];
            } else {
                foreach ($criteria['where'] as $key => $value) {
                    if ( ( $key == 'AND' || $key == 'OR' || $key == 'NOT' ) ) {
                        $sub_wheres = [];
                        foreach ($value as $sub_key => $sub_value) {
                            $sub_wheres[] = build_where_string($sub_key,$sub_value,$schema,$populate_column);
                        }
                        $wheres[] = '(' . implode(" {$key} ", $sub_wheres) . ')';
                        continue;
                    }
                    $wheres[] = build_where_string($key,$value,$schema,$populate_column);
                }
            }
        }
        if (count($wheres) > 0) $where = 'WHERE ' . implode(' AND ', $wheres);

        if (isset($criteria["order_by"])) {
            if (is_string($criteria["order_by"])){
                $order_by = "ORDER BY {$criteria['order_by']}";
                $column = $criteria['order_by'];
                if (strpos(trim($column),' ')) $column = explode(" ",trim($criteria["order_by"]))[0];
                $populate_column($column);
            }else{
                $table_column_array = parse_table_column_name($criteria["order_by"]["column"],$schema);
                $table_column = implode('.',$table_column_array);
                $order_by = "ORDER BY {$table_column} {$criteria["order_by"]["direction"]}";
                $populate_column($criteria['order_by']['column']);
            }
        }
          if (isset($criteria["begin"])) $begin = " BEGIN {$criteria["begin"]}";
          if (isset($criteria["end"])) {
              if (isset($criteria["limit"]) && $criteria["end"] > $criteria['limit']) {
                  $criteria["end"] = $criteria["limit"];
              }
              $end = " END {$criteria["end"]}";
          }
          if (isset($criteria["limit"])) $limit = " LIMIT {$criteria['limit']}";

        //print_rr( "{$where} {$order_by} {$begin} {$end} {$limit}",'tcs_db_build_criteria end');
        return  ["{$where} {$order_by} {$begin} {$end} {$limit}", $query_columns, $query_tables];
    }
    return ["", $query_columns, $query_tables];
}

function build_where_string($key,$value,$schema,&$populate_column = []) {
    $wheres = [];
    if (is_array($value) && count($value) == 2) { //deprecated
        $db_column = $key;
        $operator = $value[0];
        $data = $value[1];
    } elseif (is_array($value)) {
        $db_column = $value[0];
        $operator = $value[1];
        $data = $value[2];
    } else return [];
    $table_column_array = parse_table_column_name($db_column, $schema);
    $column = implode('.', $table_column_array);
    if ($operator == 'IN') {
        $data = "('" . implode("','", $data) . "')";
    } else {
        $data = "'{$data}'";
    }
    $wheres[] = "{$column} {$operator} {$data}";
    $populate_column($db_column);
    return $wheres;
}

function tcs_db_build_tables($data,$schema,$query_columns = [],$query_tables = []): array {
    print_rr($query_columns,'tcs_db_build_tables_cols');
    print_rr($query_tables,'tcs_db_build_tables_tables');
    print_rr($data,'tcs_db_build_tables data');
    foreach (array_filter($data) as $column) {
        if (trim($column) == "") continue;
        $db = parse_table_column_name($column,$schema);
        print_rr(i:$column,label:'tcs_db_build_tables db',full:false);
        if ( isset($schema[$db['table']]["extra_fields"][$db['column']]) ) {
            $query_columns[$db['table'].$db['column']] = $schema[$db['table']]["extra_fields"][$db['column']]['sql'];
            continue;
        }
        $query_columns[$db['table'].$db['column']] = build_sql_select_entry($db['column'],$db['table']);
        if ($db['table'] && !isset($query_tables[$db['table']])) $query_tables[$db['table']] = $schema[$db['table']]['query'];
    }
    print_rr($query_columns,'tcs_db_build_tables end');
    if (count($query_columns) === 0) $query_columns[] = ' * ';
    return [
        'columns' => implode(", ", $query_columns ),
        'column_count' => count($query_columns),
        'tables' => implode(" ", array_filter($query_tables) ),
        'table_count' => count($query_tables)
    ];
}

function build_sql_select_entry($column,$table): string {
    return "{$table}.{$column} AS {$table}_{$column}";
}

function build_select_query($db_data, $schema, $criteria, $get_distinct = false):array {

    [$criteria_string,$criteria_cols,$criteria_tabs] = tcs_db_build_criteria( $criteria,$schema);

    $table_data = tcs_db_build_tables($db_data, $schema, $criteria_cols, $criteria_tabs);
    print_rr(input: $table_data);
    if (count($db_data) == 0) {
        $table_data['columns'] = ' * ';
    }
    if (!isset($table_data['tables']) || (is_array($table_data['columns']) && count($table_data['tables']) == 0)) $table_data['tables'] = $table_data_schema[$db_data['db_table']]['query'];
    $distinct = $get_distinct ? "DISTINCT " : "";
    return ['text' => "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']} {$criteria_string}",'columns' => $table_data['columns'], 'tables' => $table_data['tables'], 'column_count' => $table_data['column_count']];

};
function parse_table_column_name($column_name,$schema) {
    if (!preg_match('/^[a-zA-Z0-9]+[._][0-9a-zA-Z_.]+$/', $column_name)) return false;
    $separator = strpos($column_name, '.') ? '.' : '_';
    $db_field = explode($separator, $column_name,2);

    if (!isset($schema[ $db_field[0] ] ) ) return ['table' => '', 'column' => $column_name];
    return ['table' => $db_field[0], 'column' => $db_field[1]];
}

function tcs_db_query(string $query, array|null $params = []) {
    print_rr($query);
    return tep_db_fetch_all(tep_db_query(query: trim(preg_replace('/\s+/', ' ', $query)), params: $params));
}
function tcs_db_query_col(string $query, array $params = []) {
    print_rr($query);
    return tep_db_fetch_col(tep_db_query(query: trim(preg_replace('/\s+/', ' ', $query)), params: $params));
}

function tep_db_fetch_assoc($stmt) {
    try {
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        tep_db_error('Fetch Array', $e->errorInfo);
    }
}
?>