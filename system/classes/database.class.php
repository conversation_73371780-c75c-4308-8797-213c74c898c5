<?php

namespace system;
use PDO;
use PDOStatement;
use PDOException;
use Exception;
use const FS_APP_ROOT;

/**
 * Custom exception for database errors with additional context
 */
class DatabaseException extends Exception {
    private string $query;
    private array $params;

    public function __construct(string $message, int $code = 0, Exception $previous = null, string $query = '', array $params = []) {
        parent::__construct($message, $code, $previous);
        $this->query = $query;
        $this->params = $params;
    }

    public function getQuery(): string {
        return $this->query;
    }

    public function getParams(): array {
        return $this->params;
    }

    public function getDetailedMessage(): string {
        return $this->getMessage() . " | Query: " . $this->query . " | Params: " . json_encode($this->params);
    }
}

class database {
    private static ?database $instance = null;
    private ?PDO $pdo;
    private string $table;
    private string $select = '*';
    private array $where = [];
    private array $orderBy = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $joins = [];
    private array $casts = [];

    private function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }

    public static function connection(?\PDO $connection = null): database {
        if (!$connection) {
            // Include the database configuration file
              require_once(FS_SYSTEM . "config/db_config.php");

            // Ensure variables are in scope
            global $db_server, $db_username, $db_password, $db_database;

            // If variables are still not set, try to get them directly
            if (empty($db_username) || empty($db_database)) {
                $tcs_database = str_replace('.', '', DOMAIN);
                $db_server = 'localhost';
                $db_username = $tcs_database ?? 'wwwcadservicescouk';
                $db_password = 'S96#1kvYuCGE';
                $db_database = $tcs_database ?? 'wwwcadservicescouk';
            }

            try {
                // Try to connect using TCP/IP
                $dsn = "mysql:host=127.0.0.1;dbname=$db_database;charset=utf8";
                $options = [
                    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES   => false,
                    PDO::ATTR_TIMEOUT            => 5,
                ];

                $connection = new PDO($dsn, $db_username, $db_password, $options);
                $connection->exec("set session sql_mode=''");
            } catch (PDOException $e) {
                // If TCP/IP fails, try with localhost
                try {
                    $dsn = "mysql:host=localhost;dbname=$db_database;charset=utf8";
                    $connection = new PDO($dsn, $db_username, $db_password, $options);
                    $connection->exec("set session sql_mode=''");
                } catch (PDOException $e2) {
                    die('Connection failed: ' . $e2->getMessage() . ' (Original error: ' . $e->getMessage() . ')' .
                        ' [Username: ' . (empty($db_username) ? 'EMPTY' : 'SET') .
                        ', Database: ' . (empty($db_database) ? 'EMPTY' : $db_database) . ']');
                }
            }
        }

        if (self::$instance === null) {
            self::$instance = new self($connection);
        }

        return self::$instance;
    }

    public static function table(string $table): database {
        $instance = self::connection();
        $instance->table = $table;
        $instance->reset();
        return $instance;
    }

    public function select(array|string $columns): database {
        $this->select = is_array($columns) ? implode(', ', $columns) : $columns;
        return $this;
    }

    public function where(array|string $column, mixed $operator = null, mixed $value = null): database {
        if (is_array($column)) {
            foreach ($column as $key => $val) {
                $this->where[] = [$key, '=', $val];
            }
            return $this;
        }

        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        $this->where[] = [$column, $operator, $value];
        return $this;
    }

    public function orderBy(string $column, string $direction = 'asc'): database {
        $this->orderBy[] = [$column, $direction];
        return $this;
    }

    public function limit(int $limit): database {
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset): database {
        $this->offset = $offset;
        return $this;
    }

    public function join(string $table, string $first, string $operator = null, string $second = null): database {
        $this->joins[] = ['inner', $table, $first, $operator, $second];
        return $this;
    }

    public function leftJoin(string $table, string $first, string $operator = null, string $second = null): database {
        $this->joins[] = ['left', $table, $first, $operator, $second];
        return $this;
    }

    public function cast(array $columns): database {
        $this->casts = $columns;
        return $this;
    }

    private function applyCasts(array $results): array {
        if (empty($this->casts)) {
            return $results;
        }

        foreach ($results as &$row) {
            foreach ($this->casts as $column => $type) {
                if (isset($row[$column])) {
                    switch ($type) {
                        case 'int':
                        case 'integer':
                            $row[$column] = (int)$row[$column];
                            break;
                        case 'float':
                        case 'double':
                            $row[$column] = (float)$row[$column];
                            break;
                        case 'bool':
                        case 'boolean':
                            $row[$column] = (bool)$row[$column];
                            break;
                        case 'json':
                        case 'array':
                            $row[$column] = json_decode($row[$column], true);
                            break;
                        case 'date':
                            $row[$column] = new \DateTime($row[$column]);
                            break;
                    }
                }
            }
        }

        return $results;
    }

    public function get(): array {
        $query = $this->buildQuery();
        $stmt = $this->executeQuery($query, $this->getBindings());
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return $this->applyCasts($results);
    }

    public function first(): ?array {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }

    public function insert(array $data): PDOStatement {
        // Check if this is a batch insert (array of arrays)
        if (isset($data[0]) && is_array($data[0])) {
            // Handle batch insert
            $batch = $data;
            $stmt = null;

            foreach ($batch as $row) {
                $stmt = $this->insert($row);
            }

            return $stmt; // Return the last statement
        }

        // Check if we have numeric keys
        if (array_keys($data) === range(0, count($data) - 1)) {
            // Get the table columns from the database
            try {
                $query = "SHOW COLUMNS FROM {$this->table}";
                $stmt = $this->pdo->query($query);
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                // Map numeric array to column names
                if (count($columns) >= count($data)) {
                    $mappedData = [];
                    foreach ($data as $index => $value) {
                        $mappedData[$columns[$index]] = $value;
                    }
                    $data = $mappedData;
                } else {
                    die('Error: Not enough columns in table to match the numeric array. Please provide an associative array with column names as keys.');
                }
            } catch (PDOException $e) {
                die('Error: Failed to get table columns. ' . $e->getMessage());
            }
        }

        $columns = implode(', ', array_map(function($col) {
            return "`$col`"; // Wrap column names in backticks
        }, array_keys($data)));

        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        $query = "INSERT INTO {$this->table} ($columns) VALUES ($placeholders)";

        return $this->executeQuery($query, array_values($data));
    }

    public function update(array $data): PDOStatement {
        $set = '';
        $values = [];

        foreach ($data as $column => $value) {
            $set .= "$column = ?, ";
            $values[] = $value;
        }
        $set = rtrim($set, ', ');

        $whereClause = $this->buildWhereClause();
        $query = "UPDATE {$this->table} SET $set WHERE $whereClause";

        return $this->executeQuery($query, array_merge($values, $this->getBindingValues()));
    }

    public function delete(): PDOStatement {
        $whereClause = $this->buildWhereClause();
        $query = "DELETE FROM {$this->table} WHERE {$whereClause}";
        return $this->executeQuery($query, $this->getBindingValues());
    }
    public function truncate(): PDOStatement {
        $query = "TRUNCATE TABLE {$this->table}";
        return $this->executeQuery($query);
    }

    private function buildQuery(): string {
        $query = "SELECT {$this->select} FROM {$this->table}";

        // Add joins
        foreach ($this->joins as $join) {
            list($type, $table, $first, $operator, $second) = $join;
            $joinType = $type === 'left' ? 'LEFT JOIN' : 'JOIN';
            $query .= " {$joinType} {$table} ON {$first} {$operator} {$second}";
        }

        // Add where conditions
        if (!empty($this->where)) {
            $query .= " WHERE " . $this->buildWhereClause();
        }

        // Add order by
        if (!empty($this->orderBy)) {
            $query .= " ORDER BY ";
            $orderClauses = [];
            foreach ($this->orderBy as $order) {
                $orderClauses[] = "{$order[0]} " . strtoupper($order[1]);
            }
            $query .= implode(', ', $orderClauses);
        }

        // Add limit and offset
        if ($this->limit !== null) {
            $query .= " LIMIT {$this->limit}";
            if ($this->offset !== null) {
                $query .= " OFFSET {$this->offset}";
            }
        }

        return $query;
    }

    private function buildWhereClause(): string {
        $conditions = [];
        foreach ($this->where as $index => $condition) {
            list($column, $operator, $value) = $condition;
            $conditions[] = "{$column} {$operator} ?";
        }
        return implode(' AND ', $conditions);
    }

    private function getBindings(): array {
        $bindings = [];
        foreach ($this->where as $index => $condition) {
            $paramName = ":param{$index}";
            $bindings[$paramName] = $condition[2];
        }
        return $bindings;
    }

    private function getBindingValues(): array {
        $values = [];
        foreach ($this->where as $condition) {
            $values[] = $condition[2];
        }
        return $values;
    }

    private function reset(): database {
        $this->select = '*';
        $this->where = [];
        $this->orderBy = [];
        $this->limit = null;
        $this->offset = null;
        $this->joins = [];
        return $this;
    }

    private function executeQuery(string $query, array $params = []): PDOStatement {
        try {
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Log detailed error information
            $this->logDatabaseError($e, $query, $params);

            // Handle error based on context (API vs web request)
            if (defined('API_RUN') && API_RUN) {
                // For API requests, throw exception to be handled by caller
                throw new DatabaseException(
                    'Database query failed: ' . $e->getMessage(),
                    $e->getCode(),
                    $e,
                    $query,
                    $params
                );
            } else {
                // For web requests, show user-friendly error
                $this->handleWebError($e, $query, $params);
            }
        }
    }

    /**
     * Log detailed database error information
     */
    private function logDatabaseError(PDOException $e, string $query, array $params = []): void {
        if (!function_exists('tcs_log')) {
            return; // Fallback if logging function not available
        }

        $errorData = [
            'error_code' => $e->getCode(),
            'error_message' => $e->getMessage(),
            'sql_state' => $e->errorInfo[0] ?? 'Unknown',
            'driver_error_code' => $e->errorInfo[1] ?? 'Unknown',
            'driver_error_message' => $e->errorInfo[2] ?? 'Unknown',
            'query' => $query,
            'parameters' => $params,
            'table' => $this->table ?? 'Unknown',
            'user_id' => $_SESSION['user_id'] ?? 'Anonymous',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'stack_trace' => $e->getTraceAsString()
        ];

        // Log to database error log
        tcs_log($errorData, 'database_errors');

        // Also log to PHP error log for critical errors
        error_log("Database Error: " . $e->getMessage() . " | Query: " . $query . " | Params: " . json_encode($params));
    }

    /**
     * Handle database errors for web requests
     */
    private function handleWebError(PDOException $e, string $query, array $params = []): void {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {

            // In debug mode, show detailed error
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px; font-family: monospace;">
                <h3>Database Error (Debug Mode)</h3>
                <p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>
                <p><strong>Query:</strong> ' . htmlspecialchars($query) . '</p>
                <p><strong>Parameters:</strong> ' . htmlspecialchars(json_encode($params)) . '</p>
                <p><strong>Table:</strong> ' . htmlspecialchars($this->table ?? 'Unknown') . '</p>
                </div>');
        } else {
            // In production, show generic error
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;">
                <h3>Database Error</h3>
                <p>A database error occurred. Please try again later or contact support if the problem persists.</p>
                <p>Error ID: ' . uniqid() . '</p>
                </div>');
        }
    }

    // Static methods for direct access
    public static function raw(string $value): string {
        return $value;
    }

    public static function query(): database {
        return self::connection();
    }

    public static function schema(): Schema {
        return new Schema();
    }
}

class Schema {
    private static function getConnection(): PDO {
        // Include the database configuration file
        require_once(FS_SYSTEM . "config/db_config.php");

        // Ensure variables are in scope
        global $db_server, $db_username, $db_password, $db_database;

        // If variables are still not set, try to get them directly
        if (empty($db_username) || empty($db_database)) {
            $tcs_database = str_replace('.', '', DOMAIN);
            $db_server = 'localhost';
            $db_username = $tcs_database;
            $db_password = 'S96#1kvYuCGE';
            $db_database = $tcs_database;
        }

        try {
            // Try to connect using TCP/IP
            $dsn = "mysql:host=127.0.0.1;dbname=$db_database;charset=utf8";
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::ATTR_TIMEOUT            => 5,
            ];

            $pdo = new PDO($dsn, $db_username, $db_password, $options);
            $pdo->exec("set session sql_mode=''");
            return $pdo;
        } catch (PDOException $e) {
            // If TCP/IP fails, try with localhost
            try {
                $dsn = "mysql:host=localhost;dbname=$db_database;charset=utf8";
                $pdo = new PDO($dsn, $db_username, $db_password, $options);
                $pdo->exec("set session sql_mode=''");
                return $pdo;
            } catch (PDOException $e2) {
                die('Connection failed: ' . $e2->getMessage() . ' (Original error: ' . $e->getMessage() . ')' .
                    ' [Username: ' . (empty($db_username) ? 'EMPTY' : 'SET') .
                    ', Database: ' . (empty($db_database) ? 'EMPTY' : $db_database) . ']');
            }
        }
    }

    public static function hasTable(string $table): bool {
        $pdo = self::getConnection();
        $query = "SHOW TABLES LIKE ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    }

    public static function hasColumn(string $table, string $column): bool {
        $pdo = self::getConnection();
        $query = "SHOW COLUMNS FROM {$table} LIKE ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    }

    public static function create(string $table, callable $callback): PDOStatement {
        $blueprint = new Blueprint($table);
        $callback($blueprint);
        return $blueprint->execute();
    }

    public static function table(string $table, callable $callback): PDOStatement {
        $blueprint = new Blueprint($table, true);
        $callback($blueprint);
        return $blueprint->execute();
    }

    public static function drop(string $table): PDOStatement {
        $query = "DROP TABLE {$table}";
        return tep_db_query($query);
    }

    public static function dropIfExists(string $table): PDOStatement {
        $query = "DROP TABLE IF EXISTS {$table}";
        return tep_db_query($query);
    }
}

class Blueprint {
    private string $table;
    private array $columns = [];
    private bool $isAlter;

    public function __construct(string $table, bool $isAlter = false) {
        $this->table = $table;
        $this->isAlter = $isAlter;
    }

    public function increments(string $name): Blueprint {
        $this->columns[] = "`{$name}` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY";
        return $this;
    }

    public function json(string $name): Blueprint {
        $this->columns[] = "`{$name}` JSON";
        return $this;
    }

    public function string(string $name, int $length = 255): Blueprint {
        $this->columns[] = "`{$name}` VARCHAR({$length})";
        return $this;
    }

    public function integer(string $name): Blueprint {
        $this->columns[] = "`{$name}` INT";
        return $this;
    }

    public function text(string $name): Blueprint {
        $this->columns[] = "`{$name}` TEXT";
        return $this;
    }

    public function timestamp(string $name): Blueprint {
        $this->columns[] = "`{$name}` TIMESTAMP";
        return $this;
    }

    public function timestamps(): Blueprint {
        $this->columns[] = "`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
        $this->columns[] = "`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
        return $this;
    }

    public function execute(): PDOStatement {
        if ($this->isAlter) {
            $query = "ALTER TABLE {$this->table} ADD " . implode(", ADD ", $this->columns);
        } else {
            $query = "CREATE TABLE {$this->table} (" . implode(", ", $this->columns) . ")";
        }
        return tep_db_query($query);
    }
}
